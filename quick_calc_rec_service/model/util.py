import os
import re
from fractions import Fraction
from typing import List

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
from asteval import Interpreter

from base_common import Constants

aeval = Interpreter()

compares = [">", "<", "="]  # 暂不支持>=,<=

units = {
    "千米": "*1000000",
    "米": "*1000",
    "分米": "*100",
    "厘米": "*10",
    "毫米": "*1",
    "km": "*1000*1000",
    "m": "*1000",
    "dm": "*100",
    "cm": "*10",
    "mm": "*1",
    "平方千米": "*1000*1000*10000",
    "平方公里": "*1000*1000*10000",
    "公顷": "*10000*10000",
    "平方米": "*10000",
    "平方分米": "*100",
    "平方厘米": "*1",
    "平方毫米": "*0.01",
    "kmW": "*1000*1000*10000",
    "mW": "*10000",
    "dmW": "*100",
    "cmW": "*1",
    "吨": "*1000000",
    "千克": "*1000",
    "克": "*1",
    "t": "*1000000",
    "kg": "*1000",
    "g": "*1",
    "年": "*360*24*3600",
    "季度": "*90*24*3600",
    "月": "*30*24*3600",
    "星期": "*7*24*3600",
    "周": "*7*24*3600",
    "日": "*24*3600",
    "天": "*24*3600",
    "半小时": "*30*60",
    "小时": "*3600",
    "时": "*3600",
    "刻": "*15*60",
    "分钟": "*60",
    "秒": "*1",
    "立方米": "*1000*1000*1000",
    "立方分米": "*1000*1000",
    "立方厘米": "*1000",
    "立方毫米": "*1",
    "mE": "*1000*1000*1000",
    "dmE": "*1000*1000",
    "cmE": "*1000",
    "mmE": "*1",
    "升": "*1000*1000",
    "毫升": "*1000",
    "mL": "*1000",
    "L": "*1000*1000",
    "元": "*100",
    "角": "*10",
    "分": "*1",
    "一": "1",
    "二": "2",
    "三": "3",
    "四": "4",
    "五": "5",
    "六": "6",
    "七": "7",
    "八": "8",
    "九": "9",
    "零": "0",
    "十": "*10",
    "百": "*100",
    "千": "*1000",
    "万": "*10000",
    "亿": "*100000000",
    "E": "**3",
    "W": "**2",
    "%": "*0.01",
    "#": "*1",
    "°": "*1",
}

chi_numbers = "一二三四五六七八九"  # ['一','二','三','四','五','六','七','八','九']

num_to_chinese = {
    1: "一",
    2: "二",
    3: "三",
    4: "四",
    5: "五",
    6: "六",
    7: "七",
    8: "八",
    9: "九",
    10: "十",
}

chinese_to_num = {
    "一": "1",
    "二": "2",
    "三": "3",
    "四": "4",
    "五": "5",
    "六": "6",
    "七": "7",
    "八": "8",
    "九": "9",
    "十": "10",
}

# 用来处理多个度量领域同用的单位，如：分
complex_units = {
    "分": {
        "cats": [["半小时", "小时", "刻", "时", "秒"], ["元", "角"]],
        "replaces": ["*60", "*1"],
    },
}

operators = ["+", "-", "/", "*"]  # 需要将识别到的字符串中x替换为*，D替换为/
spaces = [
    "B",
    "C",
    "()",
]  # 填空位置的类型还可进行添加调整

"""
0:True 对
1:False 错
2:None 问号
3:Printing 印刷体
4:Error 报错
"""


# 部分不走快速计算的情况
def quick_correcting_check_expression(pred_str):
    # 1. 检查 ($X$)*Y<Z、($X$)+Y<Z、($X$)-Y<Z 或 $X$*Y<Z、$X$+Y<Z、$X$-Y<Z、$X$/Y<Z 的形式
    if re.search(r"\(?\$\d+\$\)?[\+\-\*/]\d+<\d+", pred_str):
        return False

    # 2. 检查 Y*($X$)<Z、Y+($X$)<Z、Y-($X$)<Z 或 Y*$X$<Z、Y+$X$<Z、Y-$X$<Z、Y/$X$<Z 的形式
    if re.search(r"\d+[\+\-\*/]\(?\$\d+\$\)?<\d+", pred_str):
        return False

    # 3. 检查 Z>($X$)*Y、Z>($X$)+Y、Z>($X$)-Y 或 Z>$X$*Y、Z>$X$+Y、Z>$X$-Y、Z>$X$/Y 的形式
    if re.search(r"\d+>[\+\-\*/]\(?\$\d+\$\)?\*\d+", pred_str):
        return False

    # 4. 检查 Z>Y*($X$)、Z>Y+($X$)、Z>Y-($X$) 或 Z>Y*$X$、Z>Y+$X$、Z>Y-$X$、Z>Y/$X$ 的形式
    if re.search(r"\d+>[\+\-\*/]\d+\*\(?\$\d+\$\)?", pred_str):
        return False

    # 如果没有匹配到任何一种情况，则返回 True
    return True


# 直接匹配印刷体
def match_printing(pred_str):
    # 389790000≈400000000的情况
    pattern = re.compile(r"(\d+)≈(\d+)")
    m = pattern.search(pred_str)
    if m is not None:
        return True

    # 将单位列表拼接成一个正则表达式字符串
    unit_pattern = "(" + "|".join(re.escape(key) for key in units.keys()) + ")"

    # 带单位的比大小
    pattern = re.compile(r"(\d+)" + unit_pattern + r"([<>=])(\d+)" + unit_pattern)
    m = pattern.search(pred_str)
    if m is not None:
        return True

    # F{5}{10}-F{1}{10}=F{4}{10}
    pattern = re.compile(
        r"(F\{\d+\}\{\d+\}\s*[+\-]\s*F\{\d+\}\{\d+\})\s*=\s*(F\{\d+\}\{\d+\})"
    )
    m = pattern.search(pred_str)
    if m is not None:
        return True

    # 5.1*0.8≈(4.1)的格式
    pattern = re.compile(r"\d+\.\d+\*\d+\.\d+\s*≈\s*\(\d+\.\d+\)")
    m = pattern.search(pred_str)
    if m is not None:
        return True

    # 30*(5)<270 和 (30)*5<270
    pattern = re.compile(r"(\d+\*\(\d+\)|\(\d+\)\*\d+)\s*<\s*\d+")
    m = pattern.search(pred_str)
    if m is not None:
        return True
    return None

def change_printing_2_handwrite(pred_str):
    result = ""
    i = 0
    wrapped = False
    # 正则表达式匹配 F{n}{m} 的模式
    fraction_pattern = re.compile(r"F\{\d+\}\{\d+\}")
    m = fraction_pattern.search(pred_str)
    if m:
        pred_str = re.sub(r"F\{(\d+)\}\{(\d+)\}", r"$F{\1}{\2}$", pred_str, count=1)
        return pred_str
    else:
        while i < len(pred_str):
            # 匹配数字
            if pred_str[i].isdigit() and not wrapped:
                start = i
                while i < len(pred_str) and pred_str[i].isdigit():
                    i += 1
                result += f"${pred_str[start:i]}$"
                wrapped = True
            else:
                result += pred_str[i]
                i += 1

        pred_str = result
        return pred_str

def fix_incomplete_parentheses(pred_str):
    """
    补齐$数字$两侧不完整的括号

    逻辑：
    1. 当检测到字符串内任意位置的$数字$的任意一侧有括号，但是另一侧无括号时，需要在$数字$的另一侧补上括号
    2. 数字必须由$包裹时才匹配，否则不匹配

    Args:
        pred_str: 输入字符串，如 "$6$)*9=54"

    Returns:
        修复后的字符串，如 "($6$)*9=54"
    """
    if pred_str is None:
        return pred_str

    # 查找所有$数字$模式
    dollar_pattern = r'\$\d+\$'
    matches = list(re.finditer(dollar_pattern, pred_str))

    if not matches:
        return pred_str

    result = pred_str
    offset = 0  # 用于跟踪字符串长度变化

    for match in matches:
        start_pos = match.start() + offset
        end_pos = match.end() + offset

        # 检查左侧是否有括号
        has_left_paren = start_pos > 0 and result[start_pos - 1] == '('

        # 检查右侧是否有括号
        has_right_paren = end_pos < len(result) and result[end_pos] == ')'

        # 如果一侧有括号但另一侧没有，则补齐
        if has_left_paren and not has_right_paren:
            # 左侧有括号，右侧没有，在右侧补齐
            result = result[:end_pos] + ')' + result[end_pos:]
            offset += 1
        elif not has_left_paren and has_right_paren:
            # 右侧有括号，左侧没有，在左侧补齐
            result = result[:start_pos] + '(' + result[start_pos:]
            offset += 1

    return result

def check_pred_str(pred_str, judge_item):
    """
    0:True 对
    1:False 错
    2:None 问号
    3:Printing 印刷体
    4:Error 报错
    """

    pattern_item_valid = re.compile(r"^[=\*/\+\-\<\>≈]\$\d+\$$")
    pattern_find_printing = r"\$.*\$"
    pattern_not_valid = re.compile(r"^[+\-*/=]|[+\-*/]$|[+\-*/][+\-*/]")

    # 识别内容为空,或者识别的内容长度小于等于2
    if pred_str is None or len(pred_str) <= 2:
        return {"flag": "4", "reg_answers": [], "std_answers": []}

    # 存在未作答的情况
    if "C" in pred_str:
        return {"flag": "2", "reg_answers": [], "std_answers": []}

    # 不完整式子判断（如 '-9', '/3=', '5+', '=4', '4=8' 等）
    if pattern_not_valid.search(pred_str):
        return {"flag": "4", "reg_answers": [], "std_answers": []}

    # 识别内容没有手写体,没有$任意内容$
    if not re.search(pattern_find_printing, pred_str):

        # 判断是未作答还是印刷体,区别是未作答的情况下(等式不成立)，印刷体的情况下(等式成立)
        if match_printing(pred_str):
            return {"flag": "3", "reg_answers": [], "std_answers": []}
        else:
            final = judge_item(change_printing_2_handwrite(pred_str))
            if final is None:
                return {"flag": "2", "reg_answers": [], "std_answers": []}
            else:
                if final["flag"] == "0":
                    return final
                else:
                    return {"flag": "3", "reg_answers": [], "std_answers": []}

    # 识别的式子不完整(类似于≈$90$)
    if pattern_item_valid.fullmatch(pred_str):
        return {"flag": "4", "reg_answers": [], "std_answers": []}

    return None

# 找到最匹配的单位
def find_unit_in_string(s):
    for unit in sorted(
        units.keys(), key=len, reverse=True
    ):  # 按单位长度排序，优先匹配长的
        if s.endswith(unit):
            return unit
    return None


# # 一又二分之一改成"（1+1/2）"
def fraud_frac_replace(line):
    while re.search("\d+F{[\d\.]+}{[\d\.]+}", line):
        inds = re.search("\d+F{[\d\.]+}{[\d\.]+}", line).span()
        target = line[inds[0] : inds[1]]
        left, right = target[: target.find("F")], target[target.find("F") :]
        new_target = "(" + left + "+" + right + ")"
        line = line.replace(target, new_target)

    return line


# (1+F{3}{1})改成1F{3}{1}
def modify_fractions(line):
    # 匹配 (数字 + F{数字}{数字}) 的格式，并将其替换为 数字F{数字}{数字}
    line = re.sub(r"\((\d+)\s*\+\s*F\{([\d\.]+)\}\{([\d\.]+)\}\)", r"\1F{\2}{\3}", line)
    return line


def add_plus_between_units(expression):
    # 定义所有需要匹配并替换的规则
    patterns = [
        (r"(\d+)(dm\^2)(\d+)(cm\^2)", r"\1\2+\3\4"),  # 处理dm^2和cm^2的情况
        (r"(\d+)(m\^2)(\d+)(dm\^2)", r"\1\2+\3\4"),  # 处理m^2和dm^2的情况
        (r"(\d+)(L)(\d+)(mL)", r"\1\2+\3\4"),  # 处理L和mL的情况
    ]

    # 使用re.sub()，只有在发生替换时返回修改后的结果
    for pattern, replacement in patterns:
        # 如果匹配并替换了内容，则返回修改后的结果
        if re.search(pattern, expression):
            return re.sub(pattern, replacement, expression)

    # 如果没有替换发生，返回原始输入
    return expression


# 找有几个作答
def find_space(stem):
    n = 0
    for space in spaces:
        if stem.find(space) != -1:
            newstem = stem.replace(space, "")
            n += (len(stem) - len(newstem)) // len(space)
    return n


# 找到最优的小数位保留位数，可以比指定位数多两位
def find_best_round(num, length=2):
    # 找到最优的小数位保留位数，可以比指定位数多两位
    for i in range(0, length + 3):
        if abs(round(num, i) - num) < 1e-7:
            if i == 0:
                return int(round(num, i))
            return round(num, i)
    return round(num, length)


# 处理单位
def quick_change_unit(stem):
    stem = preprocess_units_kuohao(stem)
    # 存储初始题干
    orig_stem = stem
    keys = units.keys()
    keys = sorted(keys, key=lambda i: len(i), reverse=True)
    complex_keys = complex_units.keys()
    for key in keys:
        replace = ""
        if key in complex_keys:  # 特殊处理在多个度量都会用到的单位，如：分
            cats = complex_units[key]["cats"]
            replaces = complex_units[key]["replaces"]
            for k, cat in enumerate(cats):
                for c in cat:
                    if orig_stem.find(c) != -1:
                        replace = replaces[k]
                        break
                if replace:
                    break
            if not replace:
                replace = "*1"
        else:
            replace = units[key]  # 将单位用数量级替换，此时还没有统一某一类型的单位检查

        if stem.find(key) != -1:
            stem = stem.replace("space", "B")
            # 避免'（6千米：20米）'这种情况出现，替代单位的时候需要将'*10'和前面的数字括起来
            # 将单位和前面数字，空格合在一起，增加一个括号
            while re.search("[^\+\-\*/><=:\{\}()\[\]BC]+" + key, stem):
                try:
                    int(
                        stem[stem.find(key) + len(key)]
                    )  # 解决'6时30分'类似的情况，即度量单位后紧接数字，需要增加一个'+'
                    key_inds = re.search(
                        "[^\+\-\*/><=:\{\}()\[\]BC]+" + key, stem
                    ).span()
                    stem_cut = stem[key_inds[0] : key_inds[1]]
                    stem_cut = "(" + stem_cut.replace(key, replace + ")+")
                    stem = stem[: key_inds[0]] + stem_cut + stem[key_inds[1] :]
                except:
                    key_inds = re.search(
                        "[^\+\-\*/><=:\{\}()\[\]BC]+" + key, stem
                    ).span()
                    stem_cut = stem[key_inds[0] : key_inds[1]]
                    stem_cut = stem_cut.replace(key, replace)
                    stem = (
                        stem[: key_inds[0]] + "(" + stem_cut + ")" + stem[key_inds[1] :]
                    )
            stem = stem.replace(key, replace).replace("B", "space")

    return stem


# 替换题干
def replace_dollar(pred_str):
    stem = ""
    temp_str = ""
    answer_list = []
    start = False
    for s in pred_str:
        if start:
            if s == "$":
                start = False
                while (
                    temp_str.startswith("0")
                    and not (temp_str.startswith("0."))
                    and len(temp_str) > 1
                ):
                    temp_str = temp_str[1:]
                if temp_str == "=":
                    pass
                elif temp_str.rfind("=") != -1:
                    temp_str = temp_str[temp_str.rfind("=") + 1 :]
                answer_list.append(temp_str)
                temp_str = ""
            else:
                temp_str += s

        else:
            if s == "$":
                start = True
                stem += "B"
            else:
                stem += s
    if len(temp_str) > 0:
        while temp_str.startswith("0") and len(temp_str) > 1:
            temp_str = temp_str[1:]
        if temp_str == "=":
            pass
        elif temp_str.rfind("=") != -1:
            temp_str = temp_str[temp_str.rfind("=") + 1 :]

        answer_list.append(temp_str)

    return stem, answer_list


# 处理 stem
def preprocess_stem(stem, answer_list):
    # 如果发现是比值题，先给`:`两边的式子加上括号
    if ":" in stem and "=" in stem:
        stem = stem.replace(":", "):(")
        stem = stem.replace("=", ")=")
        stem = "(" + stem

    # Replace 'F' with '\frac' in answer_list
    answer_list = [an.replace("F", "\\frac") for an in answer_list]

    # Replace '^2' and '^3' with 'W' and 'E' respectively
    stem = stem.replace("^2", "W").replace("^3", "E")

    # Replace '张' with '*'
    stem = stem.replace("张", "*")

    # Replace 'F{}{}' with '(/)' style; handle ((2/3) / (1/2)) case to avoid exceptions
    stem = stem.replace("F{", "((").replace("}{", ")/(").replace("}", "))")
    stem = stem.replace("[", "(").replace("]", ")")

    # Replace '≈' with '@'
    stem = stem.replace("≈", "@")
    stem = stem.replace("零", "")
    # 正则表达式匹配数字+单位+"半"，并捕获单位
    pattern = r"(\d+)(年|季度|月|星期|周|天|小时|时|分钟)(半)"

    # 替换时，添加相同的单位到"半"后面
    stem = re.sub(pattern, r"\1\2半\2", stem)

    if stem.find("半") == 0:
        stem = stem.replace("半", "0.5")
    else:
        stem = stem.replace("半", "+0.5")

    return stem, answer_list


def preprocess_stem2(stem, answer_list):
    stem, answer_list = preprocess_stem(stem, answer_list)
    # 检查异常情况
    if check_stem(stem) is None:
        return None, None, None
    stem = stem.replace("(B)", "B")  # '(B)' 替换成 'B'
    if len(answer_list) == 1 and answer_list[0].find("P") != -1:
        stem = stem.replace("B", "BPB")  # 针对'$BPB$'的情况，这周情况下不用B直接代替

    stem_ori = stem
    stem = replace_cheng(
        stem
    )  # 将"几成几"转换成数字，仅支持替换 "六成五" 或"六成"这种情况
    stem = replace_zhe(
        stem
    )  # 将"一三折，四折,"转换成数字，仅支持替换 "一三折" 或"四折"这种情况
    stem = replace_ge(stem)  # 兼容个的两种情况
    stem = stem.replace("D", "/").replace(
        ":", "/"
    )  # 使用python数学运算符代替文本字符。
    # if find_space(stem) == 0 and stem.find('=') == -1 and stem.find('@') == -1:  # 1+3类题型
    #     stem += '='
    #     return stem, answer_list, stem_ori
    return stem, answer_list, stem_ori


def check_stem(stem):
    for ind, l in enumerate(stem):  # 处理E100等异常情况
        if ind == len(stem) - 1:
            break
        if l == "E":
            if stem[ind + 1] in [
                "一",
                "1",
                "二",
                "2",
                "三",
                "3",
                "四",
                "4",
                "五",
                "5",
                "六",
                "6",
                "七",
                "7",
                "八",
                "8",
                "九",
                "9",
                "零",
                "0",
                "十",
                "百",
                "千",
                "万",
                "亿",
            ]:
                return None

    for ind, l in enumerate(stem):  # 处理*接单位而形成**100的异常情况
        if ind == len(stem) - 1:
            continue
        if l == "*":
            for key in units.keys():
                if stem[ind + 1 :].startswith(key):
                    return None

    if stem.find("**") != -1:  # 处理**100等异常情况
        return None
    return True


# round() 函数对正好在两者之间的小数部分（例如0.5）的处理方式是使用"银行家舍入法"，
# 也就是将其四舍五入到离它最近的偶数。所以round(46.5) 的结果是 46，而 round(47.5) 的结果是 48。
# 为了实现普通的四舍五入（传统的四舍五入法），可以考虑下面的实现：
def traditional_round(n):
    if n - int(n) == 0.5:
        return int(n) + 1
    return round(n)


# 记录小数点后的位数
def handle_decimal(num):
    if "." in num:
        decimal_len = len(num) - num.find(".") - 1
        return decimal_len
    return 0


# 加减乘对于不同位数的处理
def process_number(num):
    num_len = len(num)
    if num_len == 1:
        return [num]  # 一位数保持原样
    elif num_len == 2:
        return [str(traditional_round(int(num) / 10) * 10)]  # 二位数四舍五入取整十
    elif num_len == 3:
        an1 = str(traditional_round((int(num) / 10)) * 10)
        an2 = str(traditional_round((int(num) / 100)) * 100)
        if an1 == an2:
            return [an1]
        return [an1, an2]  # 三位数取整十和整百
    elif num_len >= 4:
        return [
            str(traditional_round(int(num) / 100) * 100)
        ]  # 四位数及以上四舍五入取整100


# 根据符号走不同的逻辑
def judgment_symbol(sym, item1, item2):
    answer = []
    formulas = []
    if sym == "+" or sym == "-" or sym == "*":
        # 根据位数做处理
        item1_list = process_number(item1)
        item2_list = process_number(item2)
        for item1 in item1_list:
            for item2 in item2_list:
                ana = int(aeval.eval(f"{item1}{sym}{item2}"))
                answer.append(str(ana))
                formulas.append([int(item1), sym, int(item2), ana])
        return answer, formulas
    else:
        # 如果除数大于被除数直接计算，四舍五入保留一位小数（极端情况，闭环机制）
        if int(item2) > int(item1):
            answer.append(round(aeval.eval(item1 + sym + item2), 1))
            return [answer], None

        if (len(item1) == 2 and len(item2) == 1) or (
            len(item1) == 1 and len(item2) == 1
        ):
            answer = int(traditional_round(aeval.eval(item1 + sym + item2)))
            return [str(answer)], None

        else:
            # 三位数÷一位，商是四舍五入，取整十，直接作为推荐答案和判题答案。
            if len(item1) == 3 and len(item2) == 1:
                answer.append(
                    str(
                        traditional_round((int(aeval.eval(item1 + sym + item2)) / 10))
                        * 10
                    )
                )
            else:
                # 先直接两数相除然后四舍五入算答案，作为判题答案，不作为推荐答案
                answer.append(
                    str(int(traditional_round(aeval.eval(item1 + sym + item2))))
                )

            # 然后根据位数做处理,一位数保持原样,二位数四舍五入取整十,三位数取整十和整百,# 四位数及以上四舍五入取整100
            item1_list = process_number(item1)
            item2_list = process_number(item2)
            for item1 in item1_list:
                for item2 in item2_list:
                    ana = aeval.eval(f"{item1}{sym}{item2}")
                    if ana.is_integer():
                        ana = int(ana)
                        answer.append(str(ana))
                        formulas.append([int(item1), sym, int(item2), ana])
                    else:
                        answer.append(str(int(traditional_round(ana))))
            return answer, formulas


# 匹配一些未知数的情况
def simplify_result(result):
    if "." not in result:
        # 匹配以单个 '1' 开头的1个字母后接^2，替换为字母^2
        result = re.sub(r"\b1([a-zA-Z])\^2\b", r"\1^2", result)
        # 匹配以单个 '1' 开头的1个字母后接2个字母，替换为2个字母
        result = re.sub(r"\b1([a-zA-Z]{2})\b", r"\1", result)
        # 匹配以单个 '1' 开头的1个字母，替换为字母
        result = re.sub(r"\b1([a-zA-Z])\b", r"\1", result)
    return result


def find_decimal(stem):
    l = []
    ref = re.search("\d+\.\d+", stem)
    if not ref is None:
        indices = ref.span()
        d = stem[indices[0] : indices[1]]
        l.append(len(d.split(".")[-1]))
        stem = stem.replace(d, "")
        l.extend(find_decimal(stem))
    return l


def gusuan(stem: str):
    # 处理手写答案B参与乘除计算的情况
    if re.match(r"([\d\.]+)/B@([\d\.]+)", stem):
        # 数字/B@数字 -> 数字/数字@B
        match = re.match(r"([\d\.]+)/B@([\d\.]+)", stem)
        num1 = match.group(1)
        num2 = match.group(2)
        stem = f"{num1}/{num2}@B"
    elif re.match(r"B/([\d\.]+)@([\d\.]+)", stem):
        # B/数字@数字 -> 数字*数字@B
        match = re.match(r"B/([\d\.]+)@([\d\.]+)", stem)
        num1 = match.group(1)
        num2 = match.group(2)
        stem = f"{num1}*{num2}@B"
    elif re.match(r"([\d\.]+)\*B@([\d\.]+)", stem):
        # 数字*B@数字 -> 数字/数字@B
        match = re.match(r"([\d\.]+)\*B@([\d\.]+)", stem)
        num1 = match.group(1)
        num2 = match.group(2)
        stem = f"{num2}/{num1}@B"
    elif re.match(r"B\*([\d\.]+)@([\d\.]+)", stem):
        # B*数字@数字 -> 数字/数字@B
        match = re.match(r"B\*([\d\.]+)@([\d\.]+)", stem)
        num1 = match.group(1)
        num2 = match.group(2)
        stem = f"{num2}/{num1}@B"

    # 处理可能包含括号的复杂表达式
    if re.match(r"[\(\)\d\+\-\*/\.]+@(B|\(B\))", stem):
        # 提取@左侧的表达式
        left_expr = stem.split("@")[0]

        # 计算表达式中参与计算的数字个数
        # 使用正则表达式提取所有数字（包括小数）
        numbers = re.findall(r"[\d\.]+", left_expr)

        # 如果数字个数大于2，则需要化简
        if len(numbers) > 2:
            try:
                # 计算括号内的表达式
                while "(" in left_expr:
                    # 找到最内层的括号
                    inner_bracket = re.search(r"\(([^\(\)]+)\)", left_expr)
                    if inner_bracket:
                        # 计算括号内的表达式
                        bracket_expr = inner_bracket.group(1)
                        bracket_result = eval(bracket_expr)
                        # 替换括号及其内容为计算结果
                        left_expr = left_expr.replace(
                            f"({bracket_expr})", str(bracket_result)
                        )

                    # 每次处理完括号后检查数字个数
                    current_numbers = re.findall(r"[\d\.]+", left_expr)
                    if len(current_numbers) <= 2:
                        break

                # 如果处理完括号后仍有超过2个数字，继续化简
                current_numbers = re.findall(r"[\d\.]+", left_expr)
                if len(current_numbers) > 2:
                    # 先处理乘除法
                    while len(re.findall(r"[\d\.]+", left_expr)) > 2 and re.search(
                        r"[\d\.]+[\*/][\d\.]+", left_expr
                    ):
                        mul_div = re.search(r"([\d\.]+)([\*/])([\d\.]+)", left_expr)
                        if mul_div:
                            a, op, b = mul_div.groups()
                            if op == "*":
                                result = float(a) * float(b)
                            else:  # op == '/'
                                result = float(a) / float(b)
                            left_expr = left_expr.replace(f"{a}{op}{b}", str(result))

                            # 每次计算后检查数字个数
                            if len(re.findall(r"[\d\.]+", left_expr)) <= 2:
                                break

                    # 如果处理完乘除法后仍有超过2个数字，处理加减法
                    if len(re.findall(r"[\d\.]+", left_expr)) > 2:
                        while len(re.findall(r"[\d\.]+", left_expr)) > 2 and re.search(
                            r"[\d\.]+[\+\-][\d\.]+", left_expr
                        ):
                            add_sub = re.search(
                                r"([\d\.]+)([\+\-])([\d\.]+)", left_expr
                            )
                            if add_sub:
                                a, op, b = add_sub.groups()
                                if op == "+":
                                    result = float(a) + float(b)
                                else:  # op == '-'
                                    result = float(a) - float(b)
                                left_expr = left_expr.replace(
                                    f"{a}{op}{b}", str(result)
                                )

                                # 每次计算后检查数字个数
                                if len(re.findall(r"[\d\.]+", left_expr)) <= 2:
                                    break

                stem = f"{left_expr}@B"
            except Exception as e:
                # 如果计算过程中出错，保持原样
                pass

    if re.match("[\d]+[\+\-\*/]{1}[\d]+@(B|\(B\))", stem):
        stem = stem.split("@")[0]
        item1 = ""
        sym = ""
        item2 = ""
        for i in stem:
            if i in ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"]:
                if sym == "":
                    item1 += i
                else:
                    item2 += i
            elif i in ["+", "-", "*", "/"]:
                sym = i
        formulas = []
        answer, f = judgment_symbol(sym, item1, item2)
        if f is not None:
            for ff in f:
                formulas.append([f"{ff[0]}{ff[1]}{ff[2]}={ff[3]}"])
        else:
            formulas = None
        return answer, formulas

    if re.match("[\d.]+[\+\-\*/]{1}[\d.]+@(B|\(B\))", stem):
        item1 = ""
        sym = ""
        item2 = ""
        for i in stem:
            if i in ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "."]:
                if sym == "":
                    item1 += i
                else:
                    item2 += i
            elif i in ["+", "-", "*", "/"]:
                sym = i
        item1_len = handle_decimal(item1)
        if float(item1) >= 0.5 and float(item1) < 1:
            item1 = "1"

        item2_len = handle_decimal(item2)
        if float(item2) >= 0.5 and float(item2) < 1:
            item2 = "1"
        max_len = max(item1_len, item2_len)
        answer_ = []
        formulas = []
        i = 0
        if sym == "+" or sym == "-":
            item1 = str(int(float(item1) * (10**max_len)))
            item2 = str(int(float(item2) * (10**max_len)))
            answer, f = judgment_symbol(sym, item1, item2)
            for ans in answer:
                ans = traditional_round(int(ans) / (10**max_len))
                answer_.append(str(ans))
                formulas.append(
                    [
                        f"{int(f[i][0]) / (10 ** max_len)}{f[i][1]}{int(f[i][2]) / (10 ** max_len)}={ans}"
                    ]
                )
                i += 1
        elif sym == "*":
            if item1 != "1":
                item1 = str(int(float(item1) * (10**item1_len)))
            else:
                item1_len = 0

            if item2 != "1":
                item2 = str(int(float(item2) * (10**item2_len)))
            else:
                item2_len = 0

            answer, f = judgment_symbol(sym, item1, item2)
            for ans in answer:
                ans = find_best_round(int(ans) / (10 ** (item1_len + item2_len)))
                answer_.append(str(ans))
                formulas.append(
                    [
                        f"{int(f[i][0]) / (10 ** item1_len)}{f[i][1]}{int(f[i][2]) / (10 ** item2_len)}={ans}"
                    ]
                )
                i += 1
        else:
            item1 = str(int(float(item1) * (10**max_len)))
            item2 = str(int(float(item2) * (10**max_len)))
            answer_, f = judgment_symbol(sym, item1, item2)
            if f is not None:
                for ff in f:
                    ff0 = int(ff[0]) / (10**max_len)
                    if ff0.is_integer():
                        ff0 = int(ff0)
                    ff2 = int(ff[2]) / (10**max_len)
                    if ff2.is_integer():
                        ff2 = int(ff2)
                    formulas.append([f"{ff0}{ff[1]}{ff2}={ff[3]}"])

            else:
                formulas = None

        return answer_, formulas

    return None, None


# 算分子分母
def change_float_frac(ff):
    # 尝试用小的max_denominator，逐渐增大直到找到合适的分数
    fraction = Fraction(ff)

    # 第一轮尝试：分母限制为50
    frac_50 = fraction.limit_denominator(50)
    if abs(ff - frac_50) < 1e-5:
        return "\\frac{%d}{%d}" % (frac_50.numerator, frac_50.denominator)

    # 第二轮尝试：分母限制为100
    frac_100 = fraction.limit_denominator(100)
    if abs(ff - frac_100) < 1e-5:
        return "\\frac{%d}{%d}" % (frac_100.numerator, frac_100.denominator)

    # 第三轮尝试：分母限制为1000
    frac_1000 = fraction.limit_denominator(1000)
    if abs(ff - frac_1000) < 1e-7:
        return "\\frac{%d}{%d}" % (frac_1000.numerator, frac_1000.denominator)

    # 第三轮尝试：分母限制为10000
    frac_10000 = fraction.limit_denominator(10000)
    if abs(ff - frac_10000) < 1e-7:
        return "\\frac{%d}{%d}" % (frac_10000.numerator, frac_10000.denominator)
    # 如果都不合适，返回原始浮点数
    return ff


def change_float_proportion_list(ff):
    # 将float 转换成分数形式
    for i in range(0, 50):
        for j in range(1, 50):
            if abs(ff - i / j) < 1e-5:
                return "\\frac{%d}{%d}" % (i, j)
    for i in range(0, 100):
        for j in range(1, 100):
            if abs(ff - i / j) < 1e-5:
                return "\\frac{%d}{%d}" % (i, j)
    for i in range(0, 1000):
        for j in range(1, 1000):
            if abs(ff - i / j) < 1e-5:
                return "\\frac{%d}{%d}" % (i, j)
    return ff


# 根据用户作答返回结果
def ana_result(stem, result):
    if stem[-1] == ".":
        stem = stem[:-1]

    a = re.search("[abcdxyzmn]{1}", stem)
    if a and isinstance(result, str):  # 处理带有未知数的情况
        if result.find("frac") != -1:
            return result
        inds = a.span()
        letter = stem[inds[0]]
        new_stem = stem.replace(letter, "")
        ind_l = result.find(letter)
        temp = float(result[:ind_l])
        final = ana_result(new_stem, temp)
        result = str(final) + result[ind_l:]
        result = simplify_result(result)
        return result

    if "成" in stem or "折" in stem:
        unit = "成" if "成" in stem else "折"  # 根据包含的内容设置单位

        # 处理整数 1 的情况，返回"十"或"十成"/"十折"
        if result == 1:
            space1 = stem.rfind("$")
            space2 = stem.find(unit)
            return "十" if space2 > space1 else f"十{unit}"

        if unit not in stem.split("=")[1]:
            # 处理折扣非精确小数部分的情况
            return find_best_round(round(result))

        # 将小数部分四舍五入到两位
        result = round(result, 2)

        # 获取整数部分和小数部分，使用字符串处理避免浮点数精度问题
        result_str = str(result)
        if '.' in result_str:
            decimal_str = result_str.split('.')[1]
            # 确保小数部分至少有两位，不足的补0
            decimal_str = decimal_str.ljust(2, '0')
            integer_part = int(decimal_str[0])  # 获取成的整数部分（小数点后第一位）
            decimal_part = int(decimal_str[1])  # 获取成的十分位（小数点后第二位）
        else:
            # 如果没有小数部分，说明是整数，按0.X0处理
            integer_part = 0
            decimal_part = 0

        # 整数部分的中文表示
        integer_chinese = num_to_chinese.get(integer_part, "")
        # 如果小数部分是0，直接返回整数部分
        # 小数部分的中文表示
        decimal_chinese = num_to_chinese.get(decimal_part, "")
        # 返回格式化的字符串
        if "成" in stem:
            if decimal_part == 0:
                return f"{integer_chinese}{unit}"
            result = f"{integer_chinese}{unit}{decimal_chinese}"
        if "折" in stem:
            space1 = stem.rfind("$")
            space2 = stem.find("折")
            if decimal_part == 0:
                if space2 > space1:
                    return f"{integer_chinese}"
                else:
                    return f"{integer_chinese}{unit}"
            if space2 > space1:
                result = f"{integer_chinese}{decimal_chinese}"
            else:
                result = f"{integer_chinese}{decimal_chinese}{unit}"
        return result

    if stem.find("%") != -1 and stem.find("F{") == -1:  # 如果存在百分号且没有分数

        if isinstance(result, float):
            # '1/4=$5$%'
            if "=" in stem and stem.split("=")[1].find("%") != -1:
                start_index = stem.split("=")[1].find("$")
                end_index = stem.split("=")[1].rfind("$")

                if (
                    start_index != -1
                    and end_index != -1
                    and start_index < stem.split("=")[1].find("%") < end_index
                ):
                    result = find_best_round(result, 2)
                    result = f"{result * 100}%"
                    if result.endswith(".0%"):
                        result = result.replace(".0%", "%")
                else:
                    result = find_best_round(result, 2)
                # 当结果是小数并且没有小数部分时，转换为整数
            else:
                result = find_best_round(result, 2)
            return str(result)
        if isinstance(result, list):
            for r in result:
                if isinstance(r, float):
                    r = find_best_round(r, 2)
                    r = str(r)
        return result
    if stem.find(".") != -1 and (
        stem.find("%") == -1 and stem.find("F{") == -1
    ):  # 如果存在小数，则返回小数(不能同时小数，分数，百分数)
        ll = find_decimal(stem)
        length = max(ll)  # 找到小数的最大精度
        if isinstance(result, float):  # 如果进位后大小仍然一致，则可以多保留几位小数
            result = find_best_round(result, length)
        if isinstance(result, list):
            for i in range(len(result)):
                if isinstance(result[i], float):
                    result[i] = find_best_round(result[i], length)
        return result
    if (
        stem.find("F{") != -1 or stem.find(":") != -1
    ):  # 如果不存在小数，但是存在分数，则返回分数
        if isinstance(result, float):
            if abs(round(result) - result) < 1e-5:
                result = round(result)
            else:
                if result < 0:
                    result = "-" + change_float_frac(-1 * result)
                else:
                    # 如果用户作答为百分数，就返回百分数；如果用户作答是小数或者或者不是分数则返回小数；否则返回分数
                    if "=" in stem:
                        if stem.split("=")[1].find("%") != -1:
                            result = find_best_round(result, 2)
                            result = f"{result * 100}%"
                            # 当结果是小数并且没有小数部分时，转换为整数
                            if result.endswith(".0%"):
                                result = result.replace(".0%", "%")
                        elif (
                            stem.split("=")[1].find(".") != -1
                            or stem.split("=")[1].find("F{") == -1
                        ):
                            # 四舍五入保留适当的位数，解决浮点误差
                            result = round(result, 10)
                        else:
                            result = change_float_frac(result)
                    else:
                        result = change_float_frac(result)
                    # print('%%%%%%%%%%%%%%%%%%%%%%%%',result)
            return result
        if isinstance(result, list):
            final_result = []
            for r in result:
                if isinstance(r, float):
                    if abs(round(r, 2) - r) < 1e-5:
                        r = round(r, 2)
                    else:
                        r = change_float_frac(r)
                final_result.append(r)
            return final_result

    if isinstance(result, float):
        return find_best_round(result, 2)

    if isinstance(result, list):
        f = []
        for r in result:
            if isinstance(r, float):
                f.append(find_best_round(r, 2))
            else:
                f.append(r)
        return f
    else:
        return result


def replace_cheng(stem):
    ind = stem.find("成")
    if ind == -1:
        return stem
    head = stem[: ind - 1]
    if stem[ind + 1] in chi_numbers:
        tail = stem[ind + 2 :]
        replace = str(
            (chi_numbers.index(stem[ind - 1]) + 1) * 0.1
            + (chi_numbers.index(stem[ind + 1]) + 1) * 0.01
        )
    else:
        tail = stem[ind + 1 :]
        replace = str((chi_numbers.index(stem[ind - 1]) + 1) * 0.1)
    return head + replace + tail


def replace_zhe(stem):
    ind = stem.find("折")
    if ind == -1:
        return stem
    if "B折" in stem:
        return stem.replace("B折", "B")
    tail = stem[ind + 1 :]
    if ind == 2:
        replace = str(
            (chi_numbers.index(stem[ind - 2]) + 1) * 0.1
            + (chi_numbers.index(stem[ind - 1]) + 1) * 0.01
        )
    else:
        replace = str((chi_numbers.index(stem[ind - 1]) + 1) * 0.1)
    return replace + tail


def replace_ge(stem):
    if "个" in stem:
        new_stem = ""
        i = 0
        while i < len(stem):
            if stem[i] == "个":
                if (
                    i > 0
                    and i < len(stem) - 1
                    and stem[i - 1].isdigit()
                    and (stem[i + 1].isdigit() or stem[i + 1] in chinese_to_num.keys())
                ):
                    if stem[i + 1] in chinese_to_num.keys():
                        # 如果是数字字符（如 '一'、'二' 等），替换为对应的数字
                        new_stem += "*"
                        new_stem += chinese_to_num[stem[i + 1]]
                        i += 1  # 跳过下一个字符（因为我们已经处理了它）
                    else:
                        new_stem += "*"  # 添加 '*' 到新字符串中
                else:
                    new_stem += ""  # 如果条件不满足，删除 '个'
            else:
                new_stem += stem[i]  # 其他字符直接添加到新字符串中
            i += 1  # 继续处理下一个字符
        return new_stem
    else:
        return stem


def change_unit(stem):
    # 1000000000@类似题型
    if stem[-1] == "=":
        num = int(stem[:-1])
        yi = int(round(num / 100000000.0, 0))
        wan = int(round(num / 10000.0, 0))
        if yi > 0:
            return str(yi) + "亿"
        elif wan > 0:
            return str(wan) + "万"
        else:
            return str(num)
    else:
        ind = stem.find("=")
        if stem[ind + 1 :].find("万") != -1:
            num = int(stem.split("=")[0])
            return round(num / 10000.0, 2)
        elif stem[ind + 1 :].find("亿") != -1:
            num = int(stem.split("=")[0])
            return round(num / 100000000.0, 2)
        else:
            return None


def change_unit_environ(stem, answer_list):
    if len(answer_list) != 1:
        return None
    if answer_list[0].find(".") == -1:
        decimal_len = 0
    else:
        decimal_len = len(answer_list[0]) - answer_list[0].find(".") - 1

    if stem[-1] == "@":
        num = int(stem[:-1])
        if decimal_len == 0:
            yi = int(round(num / 100000000.0, decimal_len))
            wan = int(round(num / 10000.0, decimal_len))
        else:
            yi = round(num / 100000000.0, decimal_len)
            wan = round(num / 10000.0, decimal_len)
        if yi > 0:
            return str(yi) + "亿"
        elif wan > 0:
            return str(wan) + "万"
        else:
            return str(num)
    else:
        ind = stem.find("@")
        num = int(stem[:ind])
        if stem[ind + 1 :].find("万") != -1:
            if decimal_len == 0:
                return int(round(num / 10000.0, decimal_len))
            else:
                return round(num / 10000.0, decimal_len)
        elif stem[ind + 1 :].find("亿") != -1:
            if decimal_len == 0:
                return int(round(num / 100000000.0, decimal_len))
            else:
                return round(num / 100000000.0, decimal_len)
        else:
            return None


def cal_remainder(stem):
    #     print(stem,'cal_remainder')
    # 目前只支持一个除号
    for space in spaces:
        stem = stem.replace(space, "x")
    import re

    pattern = re.compile("[0-9x]+/[0-9x]+=[0-9x]+P[0-9x]+")
    m = pattern.match(stem)
    if m is None:  # 如果格式不标准，则返回None
        return None

    eq_index = stem.find("=")
    d_index = stem.find("/")
    p_index = stem.find("P")

    first_str = stem[:d_index]
    second_str = stem[d_index + 1 : eq_index]
    third_str = stem[eq_index + 1 : p_index]
    fourth_str = stem[p_index + 1 :]

    if (
        first_str == "x"
        and second_str != "x"
        and third_str != "x"
        and fourth_str != "x"
    ):
        # 计算被除数
        return int(second_str) * int(third_str) + int(fourth_str)
    elif (
        first_str != "x"
        and second_str == "x"
        and third_str != "x"
        and fourth_str != "x"
    ):
        # 计算除数
        return (int(first_str) - int(fourth_str)) // int(third_str)
    elif (
        first_str != "x"
        and second_str != "x"
        and third_str == "x"
        and fourth_str != "x"
    ):
        # 计算商
        return int(first_str) // int(second_str)
    elif (
        first_str != "x"
        and second_str != "x"
        and third_str != "x"
        and fourth_str == "x"
    ):
        # 计算余数
        return int(first_str) % int(second_str)
    elif (
        first_str != "x"
        and second_str != "x"
        and third_str == "x"
        and fourth_str == "x"
    ):
        # 计算商和余数
        return [int(first_str) // int(second_str), int(first_str) % int(second_str)]
    else:
        return None


def cal_chi(stem):
    x1 = chi_numbers.index(stem[0]) + 1
    x2 = chi_numbers.index(stem[1]) + 1
    answer_num = x1 * x2
    tens = answer_num // 10
    tails = answer_num % 10
    answer = ""
    if tens != 0:
        answer += chi_numbers[tens - 1]
        answer += "十"
    answer += chi_numbers[tails - 1]
    return answer


def judge_uncertain(stem):
    a = re.search("[abcxymn]{1}", stem)
    if a is None:
        return stem, ""
    inds = a.span()
    letter = stem[inds[0]]
    # 检查 stem 中等号的个数
    equal_sign_count = stem.count("=")

    if equal_sign_count in [0, 1] and re.match(r"[\d.]*([a-zA-Z])\+[\d.]*\1", stem):
        stem = stem.replace(letter, "*1")
        if stem[0] == "*":
            stem = stem[1:]
        stem = stem.replace("+*", "+")
        return stem, letter
    # elif re.match('[a-zA-Z]{1}\+[a-zA-Z]{1}', stem):
    #   return stem.replace(letter, '1'), letter
    elif equal_sign_count in [0, 1] and re.match(
        "[\d.]*[a-zA-Z]{1}\-[\d.]*[a-zA-Z]{1}", stem
    ):
        stem = stem.replace(letter, "*1")
        if stem[0] == "*":
            stem = stem[1:]
        stem = stem.replace("-*", "-")
        return stem, letter
    elif re.match("[\d.]*([a-zA-Z]{1})\*[\d.]*([a-zA-Z]{1})=", stem):
        match = re.match("[\d.]*([a-zA-Z]{1})\*[\d.]*([a-zA-Z]{1})=", stem)
        if match.group(1) == match.group(2):
            stem = stem.replace(letter, "*1")
            if stem[0] == "*":
                stem = stem[1:]
            stem = stem.replace("**", "*")
            return stem, letter + "^2"
        else:
            # a*c=ac
            stem = stem.replace(match.group(1), "*1").replace(match.group(2), "*1")
            if stem[0] == "*":
                stem = stem[1:]
            stem = stem.replace("**", "*")
            return stem, f"{match.group(1)}{match.group(2)}"
    # elif re.match('[\d.]+\*[\d.]+[a-zA-Z]{1}=', stem):
    #    return stem.replace(letter,'*1'),letter
    # 3a*3= or a*3=
    elif re.match("[\d.]*[a-zA-Z]{1}\*[\d.]+=", stem):
        stem = stem.replace(letter, "*1")
        if stem[0] == "*":
            stem = stem[1:]
        return stem, letter
    # 3*a= or 3*3a=
    elif re.match("[\d.]+\*[\d.]*[a-zA-Z]{1}=", stem):
        stem = stem.replace(letter, "*1")
        stem = stem.replace("**", "*")
        return stem, letter
    elif re.match("[\d.]+[a-zA-Z]{1}/s[\d.]+=", stem):
        return stem.replace(letter, "*1"), letter
    else:
        return stem, ""


def find_spaces_ind(stem):
    res = []
    spaces_sym = []
    cum = 0
    for space in spaces:
        while stem.find(space) != -1:
            r = [stem.find(space) + cum, stem.find(space) + len(space) + cum]
            stem = stem[: stem.find(space)] + stem[stem.find(space) + len(space) :]
            res.append(r)
            spaces_sym.append(space)
            cum += len(space)
    return res, spaces_sym


def judge_multi(stem):
    # 判断是否为'=()元()角'情况，如果是，返回新的stem，以及代表倍数的list。
    if find_space(stem) < 2:  # 空格数量少于2的话，不是本场景
        return stem, []
    if stem.find("=") == -1:  # 不支持没有=
        return stem, []
    if stem.find("=") != stem.rfind("="):  # 不支持两个=
        return stem, []

    spaces_inds, spaces_sym = find_spaces_ind(stem)
    #     print(spaces_inds)
    spaces_inds = np.array(spaces_inds)
    spaces_inds = spaces_inds.reshape(-1)
    spaces_inds = spaces_inds[spaces_inds.argsort()]

    if stem.find("=") >= spaces_inds.min():  # 空格位置在=号后面
        return stem, []
    num_spaces = len(spaces_sym)

    new_stem = tail = stem[: stem.find("=") + 1] + "B"
    multi_list = []

    unit_keys = units.keys()
    complex_keys = complex_units.keys()

    units_tmp = units.copy()
    for key in unit_keys:
        replace = ""
        if key in complex_keys:  # 特殊处理在多个度量都会用到的单位，如：分
            cats = complex_units[key]["cats"]
            replaces = complex_units[key]["replaces"]
            for k, cat in enumerate(cats):
                for c in cat:
                    if stem.find(c) != -1:
                        units_tmp[key] = replaces[k]
                        break
                if replace:
                    break

    for i in range(num_spaces):
        if i == num_spaces - 1:
            unit = stem[spaces_inds[i * 2 + 1] :]
            #             print(unit)
            if not unit in unit_keys:
                return stem, []
            multi_list.append(aeval.eval(units_tmp[unit][1:]))
        else:

            unit = stem[spaces_inds[i * 2 + 1] : spaces_inds[i * 2 + 2]]
            #             print(unit)
            if not unit in unit_keys:
                return stem, []
            multi_list.append(aeval.eval(units_tmp[unit][1:]))

    return new_stem, multi_list


def judge_multi_4B(stem):
    # 判断是否为'=()*12()*100'情况，如果是，返回新的stem，以及代表倍数的list。
    if find_space(stem) < 2:  # 空格数量少于2的话，不是本场景
        return stem, []
    if stem.find("=") == -1:  # 不支持没有=
        return stem, []
    if stem.find("=") != stem.rfind("="):  # 不支持两个=
        return stem, []
    pattern = re.compile(r"(\d+)=B\*(\d+)B\*(\d+)")
    m = pattern.search(stem)
    if m:
        new_stem = stem[: stem.find("=") + 1] + "B"
        unit1 = m.group(2)
        unit2 = m.group(3)
        return new_stem, [int(unit1), int(unit2)]

    return stem, []


def change_answer_multiunit_list(answer, multi_list):
    answer = round(answer)
    res = []
    for i in range(len(multi_list)):
        res.append(answer // multi_list[i])
        answer -= answer // multi_list[i] * multi_list[i]
    return res


# 将float 转换成分数形式
def change_float_frac_list(answer):
    for i in range(0, 50):
        for j in range(1, 50):
            if abs(answer - i / j) < 1e-5:
                return [i, j]
    for i in range(0, 100):
        for j in range(1, 100):
            if abs(answer - i / j) < 1e-5:
                return [i, j]
    for i in range(0, 1000):
        for j in range(1, 1000):
            if abs(answer - i / j) < 1e-5:
                return [i, j]


def convert_mixed_fraction_to_improper(mixed_fraction_str):
    """
    将带分数转换为假分数

    Args:
        mixed_fraction_str: 带分数字符串，格式如 "(2+\\frac{2}{3})"

    Returns:
        假分数字符串，格式如 "\\frac{8}{3}"，如果不是带分数则返回原字符串
    """
    import re

    # 匹配带分数格式: (整数+\frac{分子}{分母})
    pattern = r'\((\d+)\+\\frac\{(\d+)\}\{(\d+)\}\)'
    match = re.match(pattern, mixed_fraction_str)

    if match:
        whole_part = int(match.group(1))  # 整数部分
        numerator = int(match.group(2))   # 分子
        denominator = int(match.group(3)) # 分母

        # 转换为假分数: (整数部分 * 分母 + 分子) / 分母
        improper_numerator = whole_part * denominator + numerator

        return f"\\frac{{{improper_numerator}}}{{{denominator}}}"

    return mixed_fraction_str


def parentheses_frac(ss):
    # 将{}使用()代替，同事处理{1+2/3的情况}
    if ss.find("{") == -1 or ss.find("}") == -1:
        return ss
    left_indices = []
    right_indices = []
    for ind, s in enumerate(ss):
        if s == "{":
            left_indices.append(ind)
        elif s == "}":
            right_indices.append(ind)

    if len(left_indices) != len(right_indices):
        return None
    for l in left_indices[::-1]:
        for m in right_indices:
            if m > l:
                head = ss[:l]
                tail = ss[m + 1 :]
                content = ss[l + 1 : m]
                ii = content.find("/")
                if ii == -1:
                    return None  # 不是{a/b}结构
                top = content[:ii]
                bottom = content[ii + 1 :]
                k = 0  # 判断假分数的情况
                while k < len(head) and re.search("\d+", head[(-1) * (k + 1)]):
                    k += 1
                if len(head) == 0:
                    ss = (
                        head
                        + "("
                        + "("
                        + top
                        + ")"
                        + "/"
                        + "("
                        + bottom
                        + ")"
                        + ")"
                        + tail
                    )
                elif k:
                    ss = (
                        head[: (-1) * k]
                        + "("
                        + "("
                        + "("
                        + head[(-1) * k :]
                        + "*"
                        + "("
                        + bottom
                        + ")"
                        + ")"
                        + "+"
                        + top
                        + ")"
                        + "/"
                        + "("
                        + bottom
                        + ")"
                        + ")"
                        + tail
                    )
                #                 elif head[-1] in ['B','C']:
                #                     k = 1
                #                     ss = head[:(-1)*k]+'('+'('+'('+head[(-1)*k:]+'*'+'('+bottom+')' +')'+'+'+top+')'+'/'+'('+bottom+')'+')'+tail
                #                 elif head[-2:] in ['()']:
                #                     k = 2
                #                     ss = head[:(-1)*k]+'('+'('+'('+head[(-1)*k:]+'*'+'('+bottom+')' +')'+'+'+top+')'+'/'+'('+bottom+')'+')'+tail
                else:
                    ss = (
                        head
                        + "("
                        + "("
                        + top
                        + ")"
                        + "/"
                        + "("
                        + bottom
                        + ")"
                        + ")"
                        + tail
                    )
                return parentheses_frac(ss)


# 处理数字和单位的组合，将连续的"数字单位"组合添加括号
# 构建一个包含所有单位的正则表达式
unit_pattern = r"(\d+)(%s)(\d+)(%s)" % (
    r"|".join(re.escape(key) for key in units.keys()),
    r"|".join(re.escape(key) for key in units.keys()),
)


def preprocess_units_kuohao(stem):
    """
    处理数字和单位的组合，将连续的"数字单位"组合添加括号

    优化后支持n个连续的数字+单位组合（n≥2），例如：
    - "8元8角" -> "(8元8角)"
    - "8元8角5分" -> "(8元8角5分)"
    - "1时30分45秒" -> "(1时30分45秒)"

    保持向后兼容，不影响现有逻辑。
    """
    # 构建单位正则表达式，按长度排序避免短单位被长单位包含的问题
    unit_keys = sorted(units.keys(), key=len, reverse=True)
    unit_pattern_str = r"|".join(re.escape(key) for key in unit_keys)

    # 构建匹配连续数字+单位组合的正则表达式
    # 匹配模式：数字+单位+数字+单位+（可选的更多数字+单位）
    # 使用正确的语法：第一个数字+单位，然后一个或多个额外的数字+单位
    flexible_pattern = r"(\d+(?:" + unit_pattern_str + r")(?:\d+(?:" + unit_pattern_str + r"))+)"

    def replace_units(match):
        matched_text = match.group(1)
        return f"({matched_text})"

    # 先尝试使用新的灵活模式
    result = re.sub(flexible_pattern, replace_units, stem)

    # 如果新模式没有匹配到任何内容，使用原有的模式作为后备
    # 这确保了向后兼容性
    if result == stem:
        # 使用原有的模式处理恰好2个数字+单位的情况
        result = re.sub(unit_pattern, r"(\1\2\3\4)", stem)

    return result


def preprocess_units(stem, stem_ori):
    stem = preprocess_units_kuohao(stem)
    keys = units.keys()
    keys = sorted(keys, key=lambda i: len(i), reverse=True)
    complex_keys = complex_units.keys()
    for spa in spaces:  # 暂时将空格代替成其他字符，避免干扰，单位替换了再更新回来
        stem = stem.replace(spa, "B")

    for key in keys:
        replace = ""
        if key in complex_keys:  # 特殊处理在多个度量都会用到的单位，如：分
            cats = complex_units[key]["cats"]
            replaces = complex_units[key]["replaces"]
            for k, cat in enumerate(cats):
                for c in cat:
                    if stem_ori.find(c) != -1:
                        replace = replaces[k]
                        break
                if replace:
                    break
            if not replace:
                replace = "*1"
        else:
            replace = units[key]  # 将单位用数量级替换，此时还没有统一某一类型的单位检查

        if stem.find(key) != -1:
            stem = stem.replace("space", "B")
            # 避免'（6千米：20米）'这种情况出现，替代单位的时候需要将'*10'和前面的数字括起来
            # 将单位和前面数字，空格合在一起，增加一个括号
            while re.search("[^\+\-\*/><=:\{\}()\[\]BC]+" + key, stem):
                try:
                    # 解决'6时30分'类似的情况，即度量单位后紧接数字，需要增加一个'+'
                    if stem[stem.find(key) + len(key)] in [
                        "0",
                        "1",
                        "2",
                        "3",
                        "4",
                        "5",
                        "6",
                        "7",
                        "8",
                        "9",
                        "(",
                        "{",
                        "[",
                        "B",
                        "F",
                    ]:
                        key_inds = re.search(
                            "[^\+\-\*/><=:\{\}\(\)\[\]B]+" + key, stem
                        ).span()
                        stem_cut = stem[key_inds[0] : key_inds[1]]
                        stem_cut = "(" + stem_cut.replace(key, replace + ")+")
                        stem = stem[: key_inds[0]] + stem_cut + stem[key_inds[1] :]
                    else:
                        raise
                except:
                    key_inds = re.search(
                        "[^\+\-\*/><=:\{\}\(\)\[\]B]+" + key, stem
                    ).span()
                    stem_cut = stem[key_inds[0] : key_inds[1]]
                    stem_cut = stem_cut.replace(key, replace)
                    stem = (
                        stem[: key_inds[0]] + "(" + stem_cut + ")" + stem[key_inds[1] :]
                    )
            stem = stem.replace(key, replace)

    stem = parentheses_frac(
        stem
    )  # 将字符串种的{}替换成()，然后进行计算,同时处理假分数的情况
    return stem


def ana_equation(stem):
    if stem.find("=") == len(stem) - 1:
        return stem[:-1]
    for space in spaces:
        if stem.find("=") + len(space) == len(stem) - 1 and stem.endswith(space):
            return stem[: len(stem) - len(space) - 1]
    return ""


class Eval_X:
    def __init__(self, stem):
        self.stem = stem

    def run_(self, value):
        if value == 0:
            if self.stem.find("/") != -1:
                stem = self.stem.replace("x", "0.00000001")
            else:
                stem = self.stem.replace("x", "0")
        else:
            stem = self.stem.replace("x", str(value))
        return aeval.eval(stem)


def cal_number(runner, min_, max_, depth=0):
    # 计算答案为数字的一元方程,目前只支持单调连续函数
    # 添加最大递归深度限制，防止无限递归
    if depth > 100:
        return (min_ + max_) / 2, round((min_ + max_) / 2)

    if abs(runner.run_((min_ + max_) / 2)) <= 1e-9:
        return (min_ + max_) / 2, round((min_ + max_) / 2)

    if (min_ + max_) / 2 == 0:
        if min_ < 0:
            v = -1 * 1e-8
        else:
            v = 1e-8
    else:
        v = (min_ + max_) / 2

    if runner.run_(v) * runner.run_(min_) < 0:
        return cal_number(runner, min_, v, depth + 1)
    else:
        if (min_ + max_) / 2 == 0:
            if max_ < 0:
                v = -1 * 1e-8
            else:
                v = 1e-8
        else:
            v = (min_ + max_) / 2

        return cal_number(runner, v, max_, depth + 1)


def cal_equation(stem, space):
    # 计算含等式的一元方程
    equal_index = stem.find("=")
    stem = stem[:equal_index] + "-1*(" + stem[equal_index + 1 :] + ")"
    # =不在最右侧的情况，分为两种类型，答案为数字，答案为+-*/
    stem = stem.replace(space, "x")  # x代表未知数，不是乘号
    space_index = stem.find("x")
    # 如果答案位置前后有运算符，必定为答案为数字
    if stem[space_index - 1 : space_index + 2] == "(x)":
        stem = stem.replace("(x)", "x")
        space_index = stem.find("x")
    if stem[space_index - 1] in operators or stem[space_index + 1] in operators:
        if stem.count("x") > 1:
            stem_min = stem.replace("x", "-1000000", 1)
            runner_min = Eval_X(stem_min)
            ans_float_min, ans_int_min = cal_number(runner_min, -1000000000, 1000000000)

            index = stem.find("x", 1)  # 找到第二个 'x' 的位置
            stem_max = stem[:index] + "1000000" + stem[index + 1 :]
            runner_max = Eval_X(stem_max)
            ans_float_max, ans_int_max = cal_number(runner_max, -1000000000, 1000000000)

            if ans_float_min + ans_float_max < 1e-9:
                return None
            else:
                runner = Eval_X(stem)
                ans_float, ans_int = cal_number(runner, -1000000000, 1000000000)
                return ans_float  # 答案范围（-10000-+10000）,暂时只返回整数
        else:
            runner = Eval_X(stem)
            ans_float, ans_int = cal_number(runner, -1000000000, 1000000000)
            return ans_float  # 答案范围（-10000-+10000）,暂时只返回整数
    else:
        opes = []
        for ope in operators:
            try:
                if aeval.eval(stem.replace("x", ope)) == 0:
                    opes.append(ope)
            except:
                continue
    if len(opes) != 0:
        return opes

    return None


def cal_answer(stem_ori, stem):
    stem = preprocess_units(stem, stem_ori)
    answers_needed = 0
    for space in spaces:
        if stem.find(space) != -1:
            space_ = space
            answers_needed += 1
    if answers_needed > 1:  # 多个答案空，暂不支持
        return None
    if stem.find("=") != -1:  # 有=号存在的情况
        if stem.find("=") != stem.rfind("="):
            return None  # 式子中有两个=，暂时不支持
        left = ana_equation(
            stem
        )  # 判断=是否在最右侧。如等式为1+1=，1+1=()的类型,直接返回左侧带计算的部分，否则返回''
        if left:
            answer = aeval.eval(left)
        else:
            answer = cal_equation(stem, space_)  # 计算=不在最右侧的情况
        return answer


def judge_answer_blur(std_ans, reg_ans):
    # print('############# ',std_ans,reg_ans)
    if isinstance(std_ans, str) and std_ans.find("frac") != -1:
        try:
            fraud, top, bottom = std_ans.split("{")
            if len(fraud) > 5:
                std_ans = float(fraud[:-5]) + float(top[:-1]) / float(bottom[:-1])
            else:
                std_ans = float(top[:-1]) / float(bottom[:-1])
        except:
            pass
    if isinstance(std_ans, str):
        try:
            std_ans = float(std_ans)
        except:
            pass

    if isinstance(reg_ans, str) and reg_ans.find("frac") != -1:
        try:
            fraud, top, bottom = reg_ans.split("{")
            if len(fraud) > 5:
                reg_ans = float(fraud[:-5]) + float(top[:-1]) / float(bottom[:-1])
            else:
                reg_ans = float(top[:-1]) / float(bottom[:-1])
        except:
            pass
    if isinstance(reg_ans, str):
        try:
            reg_ans = float(reg_ans)
        except:
            # print(2)
            pass

    if isinstance(std_ans, int) or isinstance(std_ans, float):
        if isinstance(reg_ans, int) or isinstance(reg_ans, float):
            return abs(std_ans - reg_ans) < 1e-6
        else:  # isinstance(reg_ans,str):
            try:
                return abs(std_ans - eval(reg_ans)) < 1e-6
            except:
                return False
    else:  # isinstance(std_ans,str)
        if isinstance(reg_ans, int) or isinstance(reg_ans, float):
            try:
                return abs(eval(std_ans) - reg_ans) < 1e-6
            except:
                return False
        else:  # isinstance(reg_ans,str):
            try:
                if std_ans == reg_ans:
                    return True
                return abs(eval(std_ans) - eval(reg_ans)) < 1e-6
            except:
                return False


# 处理答案，以及比较用户的作答
def handle_answer(final, pred_str, stem, answer_list, answer):
    # 处理0.2:0.4=$$的情况
    # 使用正则表达式匹配数字:数字=、数字:小数=、小数:数字=、小数:小数=
    # pattern = re.compile(r'\d+(\.\d+)?:\d+(\.\d+)?=')
    # if pattern.match(pred_str):
    if pred_str.find(":") != -1:
        try:
            answer = change_float_proportion_list(float(answer))
        except:
            pass
        if len(answer_list) != 1:
            return final
        tail = pred_str[pred_str.find("=") + 1 :]  # 兼容:识别成了=的情况
        tail = tail.replace("$", "")
        if (tail.find(":") == -1 or tail.find("}{") == -1) and tail.find("=") != -1:
            answer_list[0] = tail.replace("=", ":")
        if answer_list[0].find("frac") != -1:  # 答案是分数
            # 检查是否为带分数，如果是则转换为假分数
            converted_answer = convert_mixed_fraction_to_improper(answer_list[0])

            if answer == answer_list[0]:
                final["flag"] = "0"
            elif answer == converted_answer:  # 检查转换后的假分数是否与标准答案相等
                final["flag"] = "0"
            else:
                final["flag"] = "1"
                final["reg_answers"] = [answer_list[0]]
                final["std_answers"] = [answer]
            return final

        elif answer_list[0].find(":") != -1:  # 答案是':'
            num0 = answer[answer.find("{") + 1 : answer.find("}")]
            num1 = answer[answer.rfind("{") + 1 : answer.rfind("}")]
            answer_str = str(num0) + ":" + str(num1)
            if answer_str == answer_list[0]:
                final["flag"] = "0"
            else:
                final["flag"] = "1"
                final["reg_answers"] = [answer_list[0]]
                final["std_answers"] = [answer_str]
            return final

        elif answer_list[0].find(".") != -1:  # 答案是小数
            # print('#################################',answer_list[0],answer)
            try:
                if answer == answer_list[0]:
                    final["flag"] = "0"
                    return final
                num0 = answer[answer.find("{") + 1 : answer.find("}")]
                num1 = answer[answer.rfind("{") + 1 : answer.rfind("}")]
                std_answer_str = str(num0) + ":" + str(num1)  # 覆盖将手写':'识别成'.',
                reg_answer_str = (
                    answer_list[0].split(".")[0] + ":" + answer_list[0].split(".")[1]
                )
                # print(std_answer_str,reg_answer_str)
                if std_answer_str == reg_answer_str:
                    final["flag"] = "0"
                    return final

                answer_p = float(num0) / float(num1)
                if abs(answer_p - float(answer_list[0])) < 1e-5:
                    final["flag"] = "0"
                else:
                    final["flag"] = "1"
                    final["reg_answers"] = [answer_list[0]]
                    final["std_answers"] = [str(find_best_round(answer_p))]
                return final
            except:
                return final

        elif answer_list[0].find("%") != -1:  # 答案是百分数
            try:
                num0 = answer[answer.find("{") + 1 : answer.find("}")]
                num1 = answer[answer.rfind("{") + 1 : answer.rfind("}")]
                answer_p = float(num0) / float(num1)
                if abs(answer_p - float(answer_list[:-1]) / 100) < 1e-5:
                    final["flag"] = "0"
                else:
                    final["flag"] = "1"
                    final["reg_answers"] = [answer_list[0]]
                    final["std_answers"] = [str(find_best_round(answer_p * 100)) + "%"]
                return final
            except:
                return final
        else:  # 答案是整数
            try:
                num0 = answer[answer.find("{") + 1 : answer.find("}")]
                num1 = answer[answer.rfind("{") + 1 : answer.rfind("}")]
                answer_tmp = int(num0) / int(num1)
                if str(answer_tmp) == answer_list[0]:
                    final["flag"] = "0"
                else:
                    final["flag"] = "1"
                    final["reg_answers"] = [answer_list[0]]
                    if int(num0) % int(num1) == 0:
                        final["std_answers"] = [
                            str(int(answer_tmp))
                        ]  # 确保整除时去掉小数部分
                    else:
                        answer_str = str(num0) + ":" + str(num1)
                        final["std_answers"] = [answer_str]
                return final
            except:
                return final

    # 仅仅支持"1212313@" 和"123256@B万/亿"的情况
    if re.match("\d{4,}@", stem) and len(answer_list) == 1:
        inds = re.match("\d{4,}@", stem).span()
        if inds[0] != 0:  # 如果不是一开头就是上面的格式，就不处理
            pass
        elif isinstance(answer, str):  # 答案是"d万"的情况，只支持d为整数
            if answer == answer_list[0]:
                final["flag"] = "0"
            else:
                final["flag"] = "1"
                final["reg_answers"] = [answer_list[0]]
                final["std_answers"] = [answer]
            return final
        elif isinstance(answer, list):
            pass
        else:
            try:
                if answer_list[0].find(".") == -1:  # 答案没有小数点时，结果约到整数
                    gap = abs(int(answer) - aeval.eval(answer_list[0]))
                else:  # 答案有小数点，默认仅支持保留两位小数
                    gap = abs(answer - aeval.eval(answer_list[0]))

                if gap < 1e-5:
                    final["flag"] = "0"
                else:
                    final["flag"] = "1"
                    final["reg_answers"] = [answer_list[0]]
                    final["std_answers"] = [answer]

                return final
            except:
                return final

    # 5/2=$2P2$的情况
    if len(answer_list) == 1 and answer_list[0].find("P") != -1:
        if isinstance(answer, list) and len(answer) == 2:
            result_str = str(answer[0]) + "P" + str(answer[1])
            if result_str == answer_list[0]:
                final["flag"] = "0"
            else:
                final["flag"] = "1"
                final["reg_answers"] = [answer_list[0]]
                final["std_answers"] = [result_str]
        else:
            return final

    elif len(answer_list) == 2 and stem.find("P") != -1:
        if isinstance(answer, list) and len(answer) == 2:
            if answer_list[0] == str(answer[0]) and answer_list[1] == str(answer[1]):
                final["flag"] = "0"
            else:
                final["flag"] = "1"
                final["reg_answers"] = [answer_list[0], answer_list[1]]
                final["std_answers"] = [answer[0], answer[1]]
        else:
            return final

    elif len(answer_list) == 0:
        return final

    # 答案有多个，是一个列表的情况
    elif isinstance(answer, list):
        answer_list = process_fractions(pred_str, answer_list)

        if len(answer_list) != len(answer):
            try:
                if any(answer_list[0] == result[0] for result in answer):
                    final["flag"] = "0"
                else:
                    final["flag"] = "1"
                    final["reg_answers"] = [answer_list[0]]
                    final["std_answers"] = answer
                return final
            except:
                return final

        flag = True
        for i in range(len(answer_list)):
            if answer[i] == answer_list[i]:
                continue
            try:
                if abs(aeval.eval(str(answer[i])) - aeval.eval(answer_list[i])) > 1e-5:
                    flag = False
                    break
            except:
                flag = False
                break
        if flag:
            final["flag"] = "0"
        else:
            for i in range(len(answer_list)):
                final["flag"] = "1"
                final["reg_answers"].append(answer_list[i])
                final["std_answers"].append(answer[i])
        return final

    elif isinstance(answer, tuple):
        # 将元组转换为列表
        answer = list(answer)
        if len(answer_list) != len(answer):
            try:
                if any(answer_list[0] == result[0] for result in answer):
                    final["flag"] = "0"
                else:
                    final["flag"] = "1"
                    final["reg_answers"] = [answer_list[0]]
                    final["std_answers"] = answer
                return final
            except:
                return final

        flag = True
        for i in range(len(answer_list)):
            if answer[i] == answer_list[i]:
                continue
            try:
                if abs(aeval.eval(str(answer[i])) - aeval.eval(answer_list[i])) > 1e-5:
                    flag = False
                    break
            except:
                flag = False
                break
        if flag:
            final["flag"] = "0"
        else:
            for i in range(len(answer_list)):
                final["flag"] = "1"
                final["reg_answers"].append(answer_list[i])
                final["std_answers"].append(answer[i])
        return final

    # 答案是一个字符串
    elif isinstance(answer, str):
        # 检查是否为带分数，如果是则转换为假分数
        converted_answer = convert_mixed_fraction_to_improper(answer_list[0])
        if answer == answer_list[0]:
            final["flag"] = "0"
        elif answer == converted_answer:  # 检查转换后的假分数是否与标准答案相等
            final["flag"] = "0"
        else:
            final["flag"] = "1"
            final["reg_answers"] = [answer_list[0]]
            final["std_answers"] = [answer]

    else:  # 答案是一个值
        try:
            python_expr = answer_list[0]
            # 如果是 LaTeX 的 \frac{}{} 格式，转换为 (a/b) 的格式
            if re.match(r"\\frac\{[^\}]+\}\{[^\}]+\}", answer_list[0]):
                python_expr = re.sub(
                    r"\\frac\{([^\}]+)\}\{([^\}]+)\}", r"(\1/\2)", answer_list[0]
                )

            if abs(answer - aeval.eval(python_expr)) < 1e-5:
                final["flag"] = "0"
            else:
                final["flag"] = "1"
                final["reg_answers"] = [answer_list[0]]
                final["std_answers"] = [find_best_round(answer)]
        except:
            if answer is None:
                return final
            else:
                final["flag"] = "1"
                final["reg_answers"] = [answer_list[0]]
                final["std_answers"] = [answer]
                return final
    return final


def handle_answer_return(result, ori_line, pred_str, stem, answer_list):
    final = {"flag": "2", "reg_answers": [], "std_answers": []}
    if result is not None:
        answer = ana_result(ori_line, result)
        if answer is None:
            return None
        if len(answer_list) == 1:
            first_judge = judge_answer_blur(answer, answer_list[0])
            if first_judge:
                final["flag"] = "0"
                return final
            else:
                final = handle_answer(final, pred_str, stem, answer_list, answer)
                return final
        else:
            final = handle_answer(final, pred_str, stem, answer_list, answer)
            return final


def cal_non_equation_type1(stem, space):
    pattern = re.compile(r"\((.*?)\)")
    matches = pattern.findall(stem)
    if not matches is None:
        for match in matches:
            evaluated_result = eval(match)
            # 将计算后的结果插入原始字符串中
            stem = stem.replace("(" + match + ")", str(evaluated_result))
    # 计算不等式的极限值
    for compare in compares:
        compare_index = stem.find(compare)
        if compare_index != -1:
            break
    stem_left = stem[:compare_index]
    stem_right = stem[compare_index + 1 :]

    pattern = re.compile(r"(\d)(B)")
    match1 = pattern.fullmatch(stem_right)
    match2 = pattern.fullmatch(stem_left)
    if match1:
        m1 = match1.group(2)
        stem = stem.replace(m1, "0+B")
    if match2:
        m2 = match2.group(2)
        stem = stem.replace(m2, "0+B")

    stem = stem.replace(space, "x")  # x代表未知数，不是乘号
    space_index = stem.find("x")
    # 情况1：><符号两侧位数相同，且符号两侧没有运算符，则填写最大/小数字使得不等式成立
    flag_ope = False
    for ope in operators:
        if stem.find(ope) != -1:
            flag_ope = True
            break
    if not flag_ope and len(stem[: stem.find(compare)]) == len(
        stem[stem.find(compare) + len(compare) :]
    ):
        # 如果x所在一侧大于另一侧
        if compare == ">" and space_index < stem.find(compare):
            for i in range(0, 10):
                if float(stem[: stem.find(compare)].replace("x", str(i))) > float(
                    stem[stem.find(compare) + len(compare) :]
                ):
                    return i
        elif compare == "<" and space_index > stem.find(compare):
            for i in range(0, 10):
                if float(
                    stem[stem.find(compare) + len(compare) :].replace("x", str(i))
                ) > float(stem[: stem.find(compare)]):
                    return i
        if compare == ">" and space_index > stem.find(compare):
            for i in range(9, -1, -1):
                if float(
                    stem[stem.find(compare) + len(compare) :].replace("x", str(i))
                ) < float(stem[: stem.find(compare)]):
                    return i
        if compare == "<" and space_index < stem.find(compare):
            for i in range(9, -1, -1):
                if float(stem[: stem.find(compare)].replace("x", str(i))) < float(
                    stem[stem.find(compare) + len(compare) :]
                ):
                    return i
    if compare_index > space_index:
        stem = (
            stem[: compare_index - (len(space) - 1)]
            + "-1*("
            + stem[compare_index + 1 - (len(space) - 1) :]
            + ")"
        )
    else:
        if compare_index == len(stem) - 1 - len(space) and space_index == len(
            stem
        ) - len(space):
            stem = stem[:compare_index] + "-1*(1*" + stem[compare_index + 1 :] + ")"
        else:
            stem = stem[:compare_index] + "-1*(" + stem[compare_index + 1 :] + ")"

    # 分为两种类型，答案为数字，答案为+-*/
    space_index = stem.find("x")

    # 情况2：如果答案位置前后有运算符，必定为答案为数字
    if stem[space_index - 1] in operators or stem[space_index + 1] in operators:
        runner = Eval_X(stem)
        ans, _ = cal_number(runner, -1000000000, 1000000000)  # 答案范围扩大到（-1000000000-+1000000000）
        ans_min, ans, ans_max = round(ans) - 1, round(ans), round(ans) + 1
        if compare == "<":
            if runner.run_(ans) == 0:
                if runner.run_(ans_min) < 0:
                    return ans_min
                else:
                    return ans_max
            elif runner.run_(ans) < 0:
                return ans
            else:
                if runner.run_(ans_min) < 0:
                    return ans_min
                else:
                    return ans_max

        elif compare == ">":
            if runner.run_(ans) == 0:
                if runner.run_(ans_max) > 0:
                    return ans_max
                else:
                    return ans_min
            elif runner.run_(ans) > 0:
                return ans
            else:
                if runner.run_(ans_min) > 0:
                    return ans_min
                else:
                    return ans_max
    # 情况3：如果答案位置前后没有运算符，必定为答案为+-*/
    else:
        opes = []
        for ope in operators:
            if compare == ">" and aeval.eval(stem.replace("x", ope)) > 1e-6:
                opes.append([ope])
            if compare == "<" and aeval.eval(stem.replace("x", ope)) < -1 * 1e-6:
                opes.append([ope])
            if compare == "=" and abs(aeval.eval(stem.replace("x", ope))) < 1e-6:
                opes.append([ope])
        if len(opes) != 0:
            return opes
    return None


def cal_non_equation_type2(stem, space):
    space_index = stem.find(space)
    if space_index == 0 or space_index == len(stem) - 1:
        return None
    stem = stem[:space_index] + "-1*(" + stem[space_index + len(space) :] + ")"
    if aeval.eval(stem) > 1e-6:
        return ">"
    elif abs(aeval.eval(stem)) < 1e-6:
        return "="
    else:
        return "<"


def calc_iou(bbox1, bbox2):
    if not isinstance(bbox1, np.ndarray):
        bbox1 = np.array(bbox1)
    if not isinstance(bbox2, np.ndarray):
        bbox2 = np.array(bbox2)
    #     print(bbox1.shape,bbox2.shape)
    (
        xmin1,
        ymin1,
        xmax1,
        ymax1,
    ) = np.split(bbox1, 4, axis=-1)
    (
        xmin2,
        ymin2,
        xmax2,
        ymax2,
    ) = np.split(bbox2, 4, axis=-1)

    area1 = (xmax1 - xmin1) * (ymax1 - ymin1)
    area2 = (xmax2 - xmin2) * (ymax2 - ymin2)

    ymin = np.maximum(ymin1, np.squeeze(ymin2, axis=-1))
    xmin = np.maximum(xmin1, np.squeeze(xmin2, axis=-1))
    ymax = np.minimum(ymax1, np.squeeze(ymax2, axis=-1))
    xmax = np.minimum(xmax1, np.squeeze(xmax2, axis=-1))

    h = np.maximum(ymax - ymin, 0)
    w = np.maximum(xmax - xmin, 0)
    intersect = h * w

    #     union = area1 + np.squeeze(area2, axis=-1) - intersect
    #     return intersect / union
    iou_1 = intersect / area1
    iou_2 = intersect / area2.reshape(1, -1)
    ious_ = np.stack((iou_1, iou_2), 0)
    ious = ious_.max(0)
    return ious


def align_answer(box, positions):
    result = []
    box = [box]
    ious = calc_iou(box, positions)[0]
    #     arg_max = ious.argmax()
    results = []
    for i, iou in enumerate(ious):
        if iou > 0.5:
            results.append(positions[i])
    return results


def angle_cos(p0, p1, p2):
    d1, d2 = (p0 - p1).astype("float"), (p2 - p1).astype("float")
    return abs(np.dot(d1, d2) / np.sqrt(np.dot(d1, d1) * np.dot(d2, d2)))


def find_squares(img):
    squares = []
    img = cv2.GaussianBlur(img, (3, 3), 0)
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    bin = cv2.Canny(gray, 10, 100, apertureSize=3)
    #     bin = cv2.dilate(bin, None, iterations=5)
    #     bin = cv2.erode(bin, None, iterations=5)
    try:
        _, contours, _hierarchy = cv2.findContours(
            bin, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
        )
    except:
        contours, _hierarchy = cv2.findContours(
            bin, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
        )
    #     print("轮廓数量：%d" % len(contours))
    index = 0
    # 轮廓遍历
    if len(contours) == 0:
        return []
    for cnt in contours:
        cnt_len = cv2.arcLength(cnt, True)  # 计算轮廓周长
        cnt = cv2.approxPolyDP(cnt, 0.02 * cnt_len, True)  # 多边形逼近
        # 条件判断逼近边的数量是否为4，轮廓面积是否大于1000，检测轮廓是否为凸的
        if len(cnt) == 4 and cv2.contourArea(cnt) > 2000 and cv2.isContourConvex(cnt):
            M = cv2.moments(cnt)  # 计算轮廓的矩
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])  # 轮廓重心

            cnt = cnt.reshape(-1, 2)
            max_cos = np.max(
                [
                    angle_cos(cnt[i], cnt[(i + 1) % 4], cnt[(i + 2) % 4])
                    for i in range(4)
                ]
            )
            # 只检测矩形（cos90° = 0）
            if max_cos < 0.1:
                index = index + 1
                squares.append(
                    [min(cnt[:, 0]), min(cnt[:, 1]), max(cnt[:, 0]), max(cnt[:, 1])]
                )
    if len(squares) == 0:
        return []
    squares = np.array(squares)
    ws = squares[:, 2] - squares[:, 0]
    hs = squares[:, 3] - squares[:, 1]
    areas = ws * hs
    areas = areas[areas > 47]
    squares = squares[areas > 47]
    if len(squares) == 0:
        return []
    #     max_a = areas.argmax()

    return squares[np.argsort(areas * -1)]


def find_circle(img):
    h, w, c = img.shape
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    circles1 = cv2.HoughCircles(
        gray,
        cv2.HOUGH_GRADIENT,
        1,
        100,
        param1=35,
        param2=35,
        minRadius=10,
        maxRadius=100,
    )
    if circles1 is None:
        return []
    circles = circles1[0, :, :]
    circles = np.int16(np.around(circles))
    boxs = []
    #     print(circles)
    for i in circles[:]:
        if (i[2] > h / 4 or i[2] > 10) and i[2] < h * 4 / 7:
            box = [i[0] - i[2], i[1] - i[2], i[0] + i[2], i[1] + i[2]]
            boxs.append(box)
    return boxs


def order_boxs(aligned_boxs, n):
    if n == 1:
        return [aligned_boxs[0]]

    target_boxs = np.array(aligned_boxs[:n])

    xs = (target_boxs[:, 2] + target_boxs[:, 0]) / 2
    ys = (target_boxs[:, 3] + target_boxs[:, 1]) / 2
    w_mean = (target_boxs[:, 2] - target_boxs[:, 0]).mean()
    orders = [-1] * len(aligned_boxs)

    inds_order_xs = np.argsort(xs)
    ordered_step1_boxs = []
    phase_boxs = []
    for k, ind in enumerate(inds_order_xs):
        if len(phase_boxs) == 0:
            phase_boxs = aligned_boxs[ind]
            x = xs[ind]
            h = ys[ind]
            continue
        if xs[ind] - x >= w_mean:
            ordered_step1_boxs.append(phase_boxs)
            phase_boxs = aligned_boxs[ind]
            x = xs[ind]
            h = ys[ind]
            continue
        if ys[ind] < h:
            ordered_step1_boxs.append(aligned_boxs[ind])
        else:
            ordered_step1_boxs.append(phase_boxs)
            phase_boxs = aligned_boxs[ind]
            x = xs[ind]
            h = ys[ind]
    ordered_step1_boxs.append(phase_boxs)
    return ordered_step1_boxs


def order_answer_boxs(boxs, method=0):
    if len(boxs) <= 1:
        return boxs
    boxs_ = np.array(boxs, dtype=np.int32)

    if method == 0:
        xs = (boxs_[:, 2] + boxs_[:, 0]) / 2
        ys = (boxs_[:, 3] + boxs_[:, 1]) / 2
    else:
        ys = (boxs_[:, 2] + boxs_[:, 0]) / 2
        xs = (boxs_[:, 3] + boxs_[:, 1]) / 2
    h_mean = (boxs_[:, 2] - boxs_[:, 0]).mean()

    inds_order_ys = np.argsort(ys)

    final_boxs = []
    tmp_inds = []
    for ind_y in inds_order_ys:
        if len(tmp_inds) == 0:
            tmp_inds.append(ind_y)
            continue
        else:
            combine = False
            if ys[ind_y] - ys[tmp_inds[-1]] < h_mean / 4:
                tmp_inds.append(ind_y)
                continue
            else:
                tmp_xs = np.array([xs[ind] for ind in tmp_inds])
                tmp_inds_order_xs = np.argsort(tmp_xs)

                for ind in tmp_inds_order_xs:
                    final_boxs.append(boxs[tmp_inds[ind]])

                tmp_inds = [ind_y]

    tmp_xs = np.array([xs[ind] for ind in tmp_inds])
    tmp_inds_order_xs = np.argsort(tmp_xs)
    for ind in tmp_inds_order_xs:
        final_boxs.append(boxs[tmp_inds[ind]])

    return final_boxs


# quick_correction_service/service/service_of_answer_detect.py 会调用
def replace_percent(line):
    ind = line.find("%")
    if ind == -1:
        return line
    tail = line[ind + 1 :]
    head = line[:ind]
    body = ""

    while len(head) > 0:
        if head[-1] in ["+", "-", "*", "/", ":", "="]:
            break
        body = head[-1] + body
        head = head[:-1]

    return head + "(" + body + "*0.01" + ")" + tail


def DrawChinese(
    img, text, position, fontSize=20, fontColor=(0, 0, 255)
):  # args-(img:numpy.ndarray, text:中文文本, position:位置, fontSize:字体大小默认20, fontColor:字体颜色默认绿色)
    cv2img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)  # cv2和PIL中颜色的hex码的储存顺序不同
    pilimg = Image.fromarray(cv2img)
    # PIL图片上打印汉字
    draw = ImageDraw.Draw(pilimg)  # 图片上打印
    font = ImageFont.truetype(
        f"{Constants.MODEL_WEIGHT_PATH}/SimHei.ttf", fontSize, encoding="utf-8"
    )  # 参数1：字体文件路径，参数2：字体大小
    draw.text(
        position, text, fontColor, font=font
    )  # 参数1：打印坐标，参数2：文本，参数3：字体颜色，参数4：字体格式
    cv2charimg = cv2.cvtColor(np.array(pilimg), cv2.COLOR_RGB2BGR)  # PIL图片转cv2 图片

    return cv2charimg


def draw_result(img, img_name, bb_, items, pred_strs_list, save_dir=""):
    for ii, item in enumerate(items):
        (bb_0, bb_1) = bb_
        poly_box = np.array(item["box"])
        xmin = poly_box[:, 0].min() + bb_0
        xmax = poly_box[:, 0].max() + bb_0
        ymin = poly_box[:, 1].min() + bb_1
        ymax = poly_box[:, 1].max() + bb_1

        if img is None:
            continue
        pred_strs = pred_strs_list[ii]

        img = DrawChinese(
            img, pred_strs[0], (xmin, ymin), fontSize=10, fontColor=(0, 0, 255)
        )
        img = cv2.rectangle(img, (xmin, ymin), (xmax, ymax), (255, 0, 0), 1)

    cv2.imwrite(os.path.join(save_dir, img_name), img)


def process_fractions(pred_str: str, answer_list: List[str]):
    """
    处理pred_str中的分数形式，对分子分母进行约分

    Args:
        pred_str: 原始识别字符串，可能包含F{($x$)}{($y$)}或F{$x$}{$y$}形式的分数
        answer_list: 从pred_str中提取的答案列表

    Returns:
        frac_answer_list: 约分后的答案列表，如果没有分数形式则返回空列表
    """

    # 检查是否存在F{($x$)}{($y$)}或F{$x$}{$y$}形式的分数
    frac_pattern = re.compile(
        r"F\{(?:\()?\$([^$]+)\$(?:\))?\}\{(?:\()?\$([^$]+)\$(?:\))?\}"
    )

    # 如果没有找到分数形式，直接返回answer_list
    if not frac_pattern.search(pred_str):
        return answer_list

    # 初始化frac_answer_list为answer_list的副本，保持顺序一致
    frac_answer_list = answer_list.copy()

    # 记录pred_str中所有美元符号的位置
    dollar_positions = []
    for i, char in enumerate(pred_str):
        if char == "$":
            dollar_positions.append(i)

    # 查找所有F{($x$)}{($y$)}或F{$x$}{$y$}形式的分数
    for match in frac_pattern.finditer(pred_str):
        # 获取分子和分母的值
        numerator_str = match.group(1)
        denominator_str = match.group(2)

        # 获取分子和分母在pred_str中的位置
        numerator_dollar_pos = match.start(1) - 1  # -1是为了找到$的位置
        denominator_dollar_pos = match.start(2) - 1  # -1是为了找到$的位置

        # 找到对应的美元符号位置
        numerator_dollar_idx = -1
        denominator_dollar_idx = -1

        for i, pos in enumerate(dollar_positions):
            if pos == numerator_dollar_pos:
                numerator_dollar_idx = i // 2  # 每两个$对应answer_list中的一个元素
            if pos == denominator_dollar_pos:
                denominator_dollar_idx = i // 2
            if numerator_dollar_idx != -1 and denominator_dollar_idx != -1:
                break

        # 确保找到了有效的索引
        if numerator_dollar_idx >= len(answer_list) or denominator_dollar_idx >= len(
            answer_list
        ):
            continue

        # 验证answer_list中的值与分子分母匹配
        if (
            answer_list[numerator_dollar_idx] == numerator_str
            and answer_list[denominator_dollar_idx] == denominator_str
        ):
            try:
                # 转换为数值类型进行约分
                numerator = (
                    float(numerator_str) if "." in numerator_str else int(numerator_str)
                )
                denominator = (
                    float(denominator_str)
                    if "." in denominator_str
                    else int(denominator_str)
                )

                # 使用Fraction进行约分
                frac = Fraction(numerator, denominator)

                # 更新frac_answer_list中对应位置的值
                frac_answer_list[numerator_dollar_idx] = str(frac.numerator)
                frac_answer_list[denominator_dollar_idx] = str(frac.denominator)
            except (ValueError, ZeroDivisionError):
                # 如果转换失败，保持原始值不变
                pass

    return frac_answer_list


if __name__ == "__main__":
    print()
